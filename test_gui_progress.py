#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试GUI进度条功能
"""

import sys
import os

# 确保src目录在Python路径中
src_dir = os.path.abspath('src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

def test_gui_import():
    """测试GUI模块是否能正常导入"""
    try:
        import guiplus
        print("✓ GUI模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ GUI模块导入失败: {e}")
        return False

def test_gui_creation():
    """测试GUI界面是否能正常创建"""
    try:
        import guiplus
        demo = guiplus.create_gui()
        print("✓ GUI界面创建成功")
        return True
    except Exception as e:
        print(f"✗ GUI界面创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试GUI进度条功能...")
    
    # 测试导入
    if not test_gui_import():
        return 1
    
    # 测试GUI创建
    if not test_gui_creation():
        return 1
    
    print("✓ 所有测试通过！")
    print("\n进度条功能已添加:")
    print("- 添加了进度文本显示框")
    print("- 添加了进度滑动条")
    print("- 翻译过程中会显示详细的进度信息")
    print("- 支持不同阶段的进度消息")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
