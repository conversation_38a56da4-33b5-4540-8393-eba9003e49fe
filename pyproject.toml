[project]
name = "NexTranslation"
version = "0.1.0"
description = "An intelligent PDF translation tool"
requires-python = ">=3.10"
dependencies = [
    "pymupdf<1.25.3",
    "tqdm",
    "tenacity",
    "numpy",
    "openai",
    "deepl",
    "gradio",
    "flask",
    "celery",
    "pdfminer.six>=20240706",
    "peewee>=3.17.8",
    "rich",
    "opencv-python",
    "onnx",          
    "onnxruntime",  
    "huggingface_hub",
    "requests",     
    "babeldoc"      
]
[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov",
    "pytest-mock",
    "pytest-asyncio",  # 如果有异步代码
]

[project.scripts]
nex-translate = "nex_translation.presentation.cli:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
# Explicitly tell hatch where the package directory is
packages = ["src/nex_translation"]
