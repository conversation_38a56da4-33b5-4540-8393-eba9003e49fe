#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NexTranslation GUI Plus
简化版GUI界面，提供基本的文件选择、翻译和取消翻译功能
在本地端口7860上运行
"""

import os
import sys
import time
import asyncio
import logging
from pathlib import Path
import tempfile
import shutil
from string import Template
import fitz  # PyMuPDF

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("nex_translation.guiplus")

# 确保logs目录存在
Path('logs').mkdir(exist_ok=True)
# 添加文件处理器
file_handler = logging.FileHandler(os.path.join('logs', 'guiplus.log'), encoding='utf-8')
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(file_handler)

try:
    # 导入必要的库
    import gradio as gr
except ImportError as e:
    logger.error(f"导入错误: {e}")
    logger.error("请确保已安装所有依赖: pip install gradio")
    sys.exit(1)

# 确保src目录在Python路径中
src_dir = os.path.abspath('src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)
    logger.info(f"已将 {src_dir} 添加到Python路径")

# 导入后端模块
try:
    from nex_translation.core.pdf_processor import translate
    from nex_translation.core.doclayout import DocLayoutModel
    from nex_translation.infrastructure.config import ConfigManager
    from nex_translation.utils.exceptions import NexTranslationError
    # 导入翻译器类
    from nex_translation.core.translator import BaseTranslator
    from nex_translation.core.google_translator import GoogleTranslator
except ImportError as e:
    logger.error(f"导入后端模块失败: {e}")
    logger.error("请确保已安装所有依赖并且项目结构正确")
    sys.exit(1)

# 全局变量
TEMP_DIR = Path(tempfile.gettempdir()) / "nex_translation"
TEMP_DIR.mkdir(exist_ok=True)
OUTPUT_DIR = Path("output")
OUTPUT_DIR.mkdir(exist_ok=True)

# 取消事件字典
cancellation_events = {}

# 全局变量存储PDF文档对象和当前页码
pdf_documents = {}

# PDF预览函数
def create_pdf_preview(pdf_path, page_num=0):
    """
    从PDF文件创建指定页码的图像预览

    Args:
        pdf_path: PDF文件路径
        page_num: 页码（从0开始）

    Returns:
        str: 图像文件路径，如果失败则返回None
    """
    if not pdf_path or not os.path.exists(pdf_path):
        return None

    try:
        # 创建临时目录用于存储预览图像
        preview_dir = Path(tempfile.gettempdir()) / "nex_translation_preview"
        preview_dir.mkdir(exist_ok=True)

        # 生成唯一的文件名，包含页码信息
        preview_filename = f"preview_{os.path.basename(pdf_path)}_page{page_num}_{int(time.time())}.png"
        preview_path = str(preview_dir / preview_filename)

        # 打开PDF文件或使用已缓存的文档对象
        if pdf_path in pdf_documents:
            doc = pdf_documents[pdf_path]
        else:
            doc = fitz.open(pdf_path)
            pdf_documents[pdf_path] = doc

        # 检查页码是否有效
        if doc.page_count > 0:
            # 确保页码在有效范围内
            page_num = max(0, min(page_num, doc.page_count - 1))
            # 获取指定页
            page = doc[page_num]
            # 渲染为图像
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x缩放以获得更好的质量
            # 保存图像
            pix.save(preview_path)
            return preview_path, doc.page_count, page_num
        else:
            return None, 0, 0
    except Exception as e:
        logger.error(f"创建PDF预览失败: {e}")
        return None, 0, 0

# 清理PDF文档对象
def cleanup_pdf_documents():
    """清理所有打开的PDF文档对象"""
    for doc in pdf_documents.values():
        try:
            doc.close()
        except Exception as e:
            logger.error(f"关闭PDF文档失败: {e}")
    pdf_documents.clear()

# 翻译处理函数
def translate_file_with_progress(file_path, progress=gr.Progress()):
    """
    处理文件翻译

    Args:
        file_path: 上传的文件路径
        progress: Gradio进度对象

    Returns:
        tuple: (状态信息, 单语文件, 双语文件)
    """
    if not file_path:
        return "请先上传PDF文件", None, None, None, None, "0/0", "0/0", "请先上传PDF文件", 0

    # 检查文件类型
    if not file_path.lower().endswith('.pdf'):
        return "只支持PDF文件", None, None, None, None, "0/0", "0/0", "只支持PDF文件", 0

    # 创建取消事件
    cancellation_event = asyncio.Event()
    file_id = os.path.basename(file_path)
    cancellation_events[file_id] = cancellation_event

    logger.info(f"开始翻译文件: {file_path}")

    try:
        # 加载配置
        config_manager = ConfigManager.get_instance()

        # 加载布局分析模型
        layout_model = DocLayoutModel.load_available()
        if not layout_model:
            logger.error("加载布局分析模型失败")
            return "错误: 加载布局分析模型失败", None, None, None, None, "0/0", "0/0", "加载布局分析模型失败", 0

        # 确定翻译服务
        service_to_use = config_manager.get_default_service()
        enabled_services = config_manager.get_enabled_services()
        if service_to_use not in enabled_services:
            logger.warning(f"服务 '{service_to_use}' 不在启用列表中: {enabled_services}。将使用默认服务")
            service_to_use = "google"  # 默认使用google翻译

        logger.info(f"使用的翻译服务: {service_to_use}")

        # 准备环境变量
        # 从配置中获取翻译服务的环境变量
        envs = {}
        translator_config = config_manager.get_translator_config(service_to_use)
        if translator_config:
            envs.update(translator_config)
            logger.info(f"已加载翻译服务 {service_to_use} 的配置")
        else:
            logger.warning(f"未找到翻译服务 {service_to_use} 的配置，将使用默认配置")

        # 为Google翻译添加优化参数
        if service_to_use == "google":
            envs["GOOGLE_TIMEOUT"] = 20  # 减少超时时间，避免长时间等待
            envs["GOOGLE_RETRIES"] = 2   # 减少重试次数
            envs["GOOGLE_BATCH_SIZE"] = 5  # 批量处理文本
            envs["GOOGLE_DELAY"] = 0.1   # 减少请求间隔
            logger.info(f"已为Google翻译设置优化参数: 超时={envs['GOOGLE_TIMEOUT']}秒, 重试={envs['GOOGLE_RETRIES']}次")

        # 确保配置中有必要的翻译服务
        if service_to_use not in config_manager.get_enabled_services():
            logger.warning(f"服务 {service_to_use} 不在启用列表中，正在添加...")
            enabled_services = config_manager.get_enabled_services()
            enabled_services.append(service_to_use)
            with ConfigManager._lock:
                config_manager._config_data["ENABLED_SERVICES"] = enabled_services
                config_manager._save_config()
            logger.info(f"已将 {service_to_use} 添加到启用服务列表")

        # 创建输出目录
        output_dir = str(OUTPUT_DIR)
        Path(output_dir).mkdir(parents=True, exist_ok=True)

        # 初始化进度
        progress(0.0, "开始翻译...")

        # 定义进度回调函数
        def progress_callback(progress_value):
            """
            适配pdf_processor.py中的回调函数
            pdf_processor.py中的回调函数只接受一个参数
            """
            try:
                # 确保进度值在0-1之间
                progress_value = max(0, min(1, float(progress_value)))
                progress_percentage = progress_value * 100

                # 根据进度值显示不同的消息
                if progress_value < 0.1:
                    progress_message = "正在分析PDF结构..."
                elif progress_value < 0.3:
                    progress_message = "正在提取文本内容..."
                elif progress_value < 0.7:
                    progress_message = f"正在翻译文本... {progress_percentage:.1f}%"
                elif progress_value < 0.9:
                    progress_message = "正在生成翻译结果..."
                else:
                    progress_message = f"即将完成... {progress_percentage:.1f}%"

                if hasattr(progress, '__call__'):
                    progress(progress_value, progress_message)

                logger.info(f"翻译进度: {progress_percentage:.1f}% - {progress_message}")
            except Exception as e:
                # 如果更新进度失败，记录错误但不中断翻译过程
                logger.debug(f"更新进度失败: {e}")

        # 直接调用translate函数
        # 确保设置正确的语言参数
        # 根据CPU核心数动态设置线程数，但不超过8个
        import multiprocessing
        thread_count = min(8, max(4, multiprocessing.cpu_count()))

        logger.info("开始调用translate函数进行翻译...")
        logger.info(f"翻译参数: 服务={service_to_use}, 输入语言=en, 输出语言=zh-CN, 线程数={thread_count}")
        logger.info(f"环境变量: {envs}")

        # 确保输入文件存在
        if not os.path.exists(file_path):
            logger.error(f"输入文件不存在: {file_path}")
            return "错误: 输入文件不存在", None, None, None, None, "0/0", "0/0", "输入文件不存在", 0

        # 确保输出目录存在
        Path(output_dir).mkdir(parents=True, exist_ok=True)

        # 强制忽略缓存，确保进行实际翻译
        result_files = translate(
            files=[file_path],
            output=output_dir,
            pages=None,  # 翻译所有页面
            lang_in="en",  # 输入语言为英文
            lang_out="zh-CN",  # 输出语言为中文
            service=service_to_use,
            thread=thread_count,  # 使用动态线程数进行并行翻译
            vfont="",  # 默认值
            vchar="",  # 默认值
            compatible=False,
            cancellation_event=cancellation_event,
            model=layout_model,
            envs=envs,
            prompt=None,  # 使用默认提示
            skip_subset_fonts=False,
            ignore_cache=True,  # 强制忽略缓存，确保进行实际翻译
            callback=progress_callback
        )

        logger.info(f"翻译完成，结果文件: {result_files}")

        # 清理取消事件
        if file_id in cancellation_events:
            del cancellation_events[file_id]

        # 完成进度
        progress(1.0, "翻译完成!")

        # 返回结果文件路径
        if result_files and len(result_files) > 0:
            mono_path, dual_path = result_files[0]

            # 更新全局变量，存储当前翻译结果文件路径
            current_files['mono'] = mono_path
            current_files['dual'] = dual_path

            # 重置当前页码
            current_pages['mono'] = 0
            current_pages['dual'] = 0

            # 创建PDF预览（第一页）
            mono_preview_path, mono_page_count, _ = create_pdf_preview(mono_path, 0)
            dual_preview_path, dual_page_count, _ = create_pdf_preview(dual_path, 0)

            # 构建页码信息
            mono_page_info = f"1/{mono_page_count}" if mono_page_count > 0 else "0/0"
            dual_page_info = f"1/{dual_page_count}" if dual_page_count > 0 else "0/0"

            logger.info(f"生成预览图像: 单语={mono_preview_path}, 双语={dual_preview_path}")
            logger.info(f"页数信息: 单语={mono_page_info}, 双语={dual_page_info}")

            return "翻译完成!", mono_path, dual_path, mono_preview_path, dual_preview_path, mono_page_info, dual_page_info, "翻译完成!", 100
        else:
            return "翻译未返回结果文件", None, None, None, None, "0/0", "0/0", "翻译失败", 0

    except Exception as e:
        logger.error(f"翻译失败: {e}")
        import traceback
        traceback.print_exc()
        return f"翻译失败: {str(e)}", None, None, None, None, "0/0", "0/0", f"翻译失败: {str(e)}", 0

# 存储当前页码的字典
current_pages = {
    'mono': 0,
    'dual': 0
}

# 全局变量存储当前翻译结果文件路径
current_files = {
    'mono': None,
    'dual': None
}

# 取消翻译函数
def cancel_translation(file_path):
    """
    取消正在进行的翻译任务

    Args:
        file_path: 文件路径

    Returns:
        str: 状态信息
    """
    if not file_path:
        return "没有正在进行的翻译任务"

    file_id = os.path.basename(file_path)
    if file_id in cancellation_events:
        cancellation_events[file_id].set()
        logger.info(f"已发送取消信号: {file_id}")
        # 清理PDF文档对象
        cleanup_pdf_documents()
        return "已取消翻译任务"
    else:
        return "没有找到对应的翻译任务"

# 创建Gradio界面
def create_gui():
    """创建Gradio界面"""
    with gr.Blocks(title="NexTranslation Plus") as demo:
        gr.Markdown("# NexTranslation Plus")
        gr.Markdown("### PDF翻译工具 - 简化版")

        # 上方区域：文件上传和控制按钮
        with gr.Row():
            # 文件上传区域
            file_input = gr.File(
                label="上传PDF文件",
                file_types=[".pdf"],
                type="filepath"
            )

        with gr.Row():
            # 按钮区域
            translate_btn = gr.Button("开始翻译", variant="primary")
            cancel_btn = gr.Button("取消翻译", variant="stop")

        # 状态信息
        status_info = gr.Textbox(label="状态", value="请上传PDF文件并点击'开始翻译'按钮")

        # 进度显示区域
        with gr.Row():
            progress_text = gr.Textbox(label="翻译进度", value="", interactive=False)
        with gr.Row():
            progress_slider = gr.Slider(
                minimum=0,
                maximum=100,
                value=0,
                label="进度条",
                interactive=False,
                show_label=True
            )

        # 下方区域：结果预览区域
        with gr.Row():
            # 结果预览区域
            with gr.Tab("单语结果"):
                with gr.Row():
                    mono_output = gr.File(label="单语翻译结果")
                with gr.Row():
                    # 添加图片预览窗口
                    mono_preview = gr.Image(label="单语翻译预览", type="filepath")
                with gr.Row():
                    # 添加翻页控件
                    with gr.Column(scale=1):
                        mono_prev_btn = gr.Button("上一页")
                    with gr.Column(scale=2):
                        mono_page_info = gr.Textbox(label="页码信息", value="0/0", interactive=False)
                    with gr.Column(scale=1):
                        mono_next_btn = gr.Button("下一页")

            with gr.Tab("双语结果"):
                with gr.Row():
                    dual_output = gr.File(label="双语翻译结果")
                with gr.Row():
                    # 添加图片预览窗口
                    dual_preview = gr.Image(label="双语翻译预览", type="filepath")
                with gr.Row():
                    # 添加翻页控件
                    with gr.Column(scale=1):
                        dual_prev_btn = gr.Button("上一页")
                    with gr.Column(scale=2):
                        dual_page_info = gr.Textbox(label="页码信息", value="0/0", interactive=False)
                    with gr.Column(scale=1):
                        dual_next_btn = gr.Button("下一页")

        # 设置事件处理
        translate_btn.click(
            fn=translate_file_with_progress,
            inputs=[file_input],
            outputs=[status_info, mono_output, dual_output, mono_preview, dual_preview, mono_page_info, dual_page_info, progress_text, progress_slider]
        )

        # 修改取消按钮事件处理函数，确保清除预览窗口
        def on_cancel(file_path):
            result = cancel_translation(file_path)
            return result, None, None, None, None, "0/0", "0/0", "已取消翻译", 0

        cancel_btn.click(
            fn=on_cancel,
            inputs=[file_input],
            outputs=[status_info, mono_output, dual_output, mono_preview, dual_preview, mono_page_info, dual_page_info, progress_text, progress_slider]
        )

        # 文件上传事件
        def on_file_change(file_path):
            """文件上传事件处理函数"""
            if file_path:
                return "已选择文件，点击'开始翻译'按钮开始翻译", None, None, None, None, "0/0", "0/0", "准备就绪", 0
            else:
                return "请上传PDF文件", None, None, None, None, "0/0", "0/0", "", 0

        file_input.change(
            fn=on_file_change,
            inputs=[file_input],
            outputs=[status_info, mono_output, dual_output, mono_preview, dual_preview, mono_page_info, dual_page_info, progress_text, progress_slider]
        )

        # 单语结果翻页事件
        def mono_prev_click():
            """单语结果上一页"""
            try:
                # 使用全局变量中存储的文件路径
                file_path = current_files['mono']
                if not file_path or not os.path.exists(file_path):
                    return mono_preview.value, mono_page_info.value

                # 解析当前页码信息
                parts = mono_page_info.value.split('/')
                if len(parts) != 2:
                    return mono_preview.value, mono_page_info.value

                current_page = int(parts[0])
                total_pages = int(parts[1])

                if current_page <= 1 or total_pages <= 1:
                    # 已经是第一页或只有一页，不需要翻页
                    return mono_preview.value, mono_page_info.value

                # 计算新的页码
                new_page = current_page - 1
                current_pages['mono'] = new_page - 1  # 更新全局页码 (索引从0开始)

                # 创建新的预览
                preview_path, _, _ = create_pdf_preview(file_path, new_page - 1)

                # 更新页码信息
                new_page_info = f"{new_page}/{total_pages}"

                logger.info(f"翻页: 单语上一页, 文件: {file_path}, 页码: {new_page}/{total_pages}")

                return preview_path, new_page_info
            except Exception as e:
                logger.error(f"单语上一页失败: {e}")
                import traceback
                traceback.print_exc()
                return mono_preview.value, mono_page_info.value

        def mono_next_click():
            """单语结果下一页"""
            try:
                # 使用全局变量中存储的文件路径
                file_path = current_files['mono']
                if not file_path or not os.path.exists(file_path):
                    return mono_preview.value, mono_page_info.value

                # 解析当前页码信息
                parts = mono_page_info.value.split('/')
                if len(parts) != 2:
                    return mono_preview.value, mono_page_info.value

                current_page = int(parts[0])
                total_pages = int(parts[1])

                if current_page >= total_pages:
                    # 已经是最后一页，不需要翻页
                    return mono_preview.value, mono_page_info.value

                # 计算新的页码
                new_page = current_page + 1
                current_pages['mono'] = new_page - 1  # 更新全局页码 (索引从0开始)

                # 创建新的预览
                preview_path, _, _ = create_pdf_preview(file_path, new_page - 1)

                # 更新页码信息
                new_page_info = f"{new_page}/{total_pages}"

                logger.info(f"翻页: 单语下一页, 文件: {file_path}, 页码: {new_page}/{total_pages}")

                return preview_path, new_page_info
            except Exception as e:
                logger.error(f"单语下一页失败: {e}")
                import traceback
                traceback.print_exc()
                return mono_preview.value, mono_page_info.value

        mono_prev_btn.click(
            fn=mono_prev_click,
            inputs=[],
            outputs=[mono_preview, mono_page_info]
        )

        mono_next_btn.click(
            fn=mono_next_click,
            inputs=[],
            outputs=[mono_preview, mono_page_info]
        )

        # 双语结果翻页事件
        def dual_prev_click():
            """双语结果上一页"""
            try:
                # 使用全局变量中存储的文件路径
                file_path = current_files['dual']
                if not file_path or not os.path.exists(file_path):
                    return dual_preview.value, dual_page_info.value

                # 解析当前页码信息
                parts = dual_page_info.value.split('/')
                if len(parts) != 2:
                    return dual_preview.value, dual_page_info.value

                current_page = int(parts[0])
                total_pages = int(parts[1])

                if current_page <= 1 or total_pages <= 1:
                    # 已经是第一页或只有一页，不需要翻页
                    return dual_preview.value, dual_page_info.value

                # 计算新的页码
                new_page = current_page - 1
                current_pages['dual'] = new_page - 1  # 更新全局页码 (索引从0开始)

                # 创建新的预览
                preview_path, _, _ = create_pdf_preview(file_path, new_page - 1)

                # 更新页码信息
                new_page_info = f"{new_page}/{total_pages}"

                logger.info(f"翻页: 双语上一页, 文件: {file_path}, 页码: {new_page}/{total_pages}")

                return preview_path, new_page_info
            except Exception as e:
                logger.error(f"双语上一页失败: {e}")
                import traceback
                traceback.print_exc()
                return dual_preview.value, dual_page_info.value

        def dual_next_click():
            """双语结果下一页"""
            try:
                # 使用全局变量中存储的文件路径
                file_path = current_files['dual']
                if not file_path or not os.path.exists(file_path):
                    return dual_preview.value, dual_page_info.value

                # 解析当前页码信息
                parts = dual_page_info.value.split('/')
                if len(parts) != 2:
                    return dual_preview.value, dual_page_info.value

                current_page = int(parts[0])
                total_pages = int(parts[1])

                if current_page >= total_pages:
                    # 已经是最后一页，不需要翻页
                    return dual_preview.value, dual_page_info.value

                # 计算新的页码
                new_page = current_page + 1
                current_pages['dual'] = new_page - 1  # 更新全局页码 (索引从0开始)

                # 创建新的预览
                preview_path, _, _ = create_pdf_preview(file_path, new_page - 1)

                # 更新页码信息
                new_page_info = f"{new_page}/{total_pages}"

                logger.info(f"翻页: 双语下一页, 文件: {file_path}, 页码: {new_page}/{total_pages}")

                return preview_path, new_page_info
            except Exception as e:
                logger.error(f"双语下一页失败: {e}")
                import traceback
                traceback.print_exc()
                return dual_preview.value, dual_page_info.value

        dual_prev_btn.click(
            fn=dual_prev_click,
            inputs=[],
            outputs=[dual_preview, dual_page_info]
        )

        dual_next_btn.click(
            fn=dual_next_click,
            inputs=[],
            outputs=[dual_preview, dual_page_info]
        )

    return demo

def main():
    """主函数"""
    try:
        logger.info("正在启动NexTranslation Plus GUI...")

        # 初始化配置
        logger.info("正在加载配置...")
        # 从配置中获取默认服务和启用的服务
        config_manager = ConfigManager.get_instance()
        default_service = config_manager.get_default_service()
        enabled_services = config_manager.get_enabled_services()
        logger.info(f"默认翻译服务: {default_service}")
        logger.info(f"启用的翻译服务: {enabled_services}")

        # 加载布局分析模型
        logger.info("正在加载布局分析模型...")
        layout_model = DocLayoutModel.load_available()
        if not layout_model:
            logger.error("加载布局分析模型失败，请确保模型文件存在")
            return 1
        logger.info("布局分析模型已加载")

        # 创建GUI界面
        demo = create_gui()

        # 启动服务器
        logger.info("正在启动服务器，端口: 7860")
        demo.launch(
            server_name="127.0.0.1",
            server_port=7860,
            share=False,
            inbrowser=True
        )

        return 0
    except NexTranslationError as e:
        logger.error(f"启动失败: {e}")
        return 1
    except Exception as e:
        logger.error(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        # 清理资源
        logger.info("正在清理资源...")
        cleanup_pdf_documents()
        logger.info("资源清理完成")

if __name__ == "__main__":
    sys.exit(main())
