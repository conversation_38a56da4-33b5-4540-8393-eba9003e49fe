nex_translation/
├── docs/
│   ├── Requirements.md          # 需求文档
│   ├── LayeredArchitecture.md   # 架构设计
│   └── ProjectOverview.md       # 项目概述
├── src/
│   ├── nex_translation/
│   │   ├── __init__.py
│   │   ├── core/               # 核心业务逻辑层
│   │   │   ├── pdf_processor.py     # PDF解析与处理
│   │   │   ├── converter.py
│   │   │   ├── doclayout.py
│   │   │   ├── pdfinterpreter.py
│   │   │   ├── translator.py        # 翻译服务集成
│   │   │   └── google_translator.py   # Google翻译实现
│   │   ├── infrastructure/     # 基础设施层
│   │   │   ├── cache.py            # 本地缓存
│   │   │   └── config.py           # 配置管理
│   │   ├── presentation/      # 表现层
│   │   │   ├── gui.py             # Gradio界面
│   │   │   └── cli.py             # 命令行接口
│   │   └── utils/             # 工具类
│   │       ├── logger.py          # 日志工具
│   │       └── exceptions.py      # 异常定义
├── tests/                     # 测试目录
│   ├── __init__.py
│   ├── test_pdf_processor.py
│   └── test_translator.py
├── README.md
├── pyproject.toml            # 项目配置
└── setup.py                  # 安装脚本